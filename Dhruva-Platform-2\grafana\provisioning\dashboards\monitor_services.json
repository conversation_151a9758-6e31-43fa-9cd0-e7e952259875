{"id": null, "title": "Monitor Services", "tags": ["prometheus"], "style": "dark", "timezone": "browser", "editable": true, "hideControls": false, "sharedCrosshair": true, "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "decimals": 1, "editable": true, "error": false, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 0}, "hideTimeOverride": true, "id": 1, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "s", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "(time() - process_start_time_seconds{instance=\"localhost:9090\",job=\"prometheus\"})", "interval": "10s", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 10}], "thresholds": "", "timeFrom": "10s", "timeShift": null, "title": "Prometheus Uptime", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "decimals": 2, "editable": true, "error": false, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 0}, "hideTimeOverride": true, "id": 5, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(container_memory_usage_bytes{container_label_org_label_schema_group=\"monitoring\"})", "format": "time_series", "interval": "10s", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 10}], "thresholds": "", "timeFrom": "10s", "timeShift": null, "title": "Memory Usage", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 0}, "hideTimeOverride": true, "id": 3, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "prometheus_tsdb_head_chunks", "interval": "10s", "intervalFactor": 1, "refId": "A", "step": 10}], "thresholds": "", "timeFrom": "10s", "timeShift": null, "title": "In-Memory Chunks", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 0}, "hideTimeOverride": true, "id": 2, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "prometheus_tsdb_head_series", "interval": "10s", "intervalFactor": 1, "refId": "A", "step": 10}], "thresholds": "", "timeFrom": "10s", "timeShift": null, "title": "In-Memory Series", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 3}, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name) (rate(container_cpu_user_seconds_total{container_label_org_label_schema_group=\"monitoring\"}[1m]) * 100  / scalar(count(node_cpu{mode=\"user\"})))", "format": "time_series", "hide": true, "intervalFactor": 10, "legendFormat": "{{ name }}", "refId": "A", "step": 10}, {"expr": "sum by (name) (rate(container_cpu_user_seconds_total{container_label_org_label_schema_group=\"monitoring\"}[1m]) * 100  / scalar(count(node_cpu_seconds_total{mode=\"user\"})))", "format": "time_series", "intervalFactor": 10, "legendFormat": "{{ name }}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Container CPU Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 10}, "id": 7, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (name) (container_memory_usage_bytes{container_label_org_label_schema_group=\"monitoring\"})", "format": "time_series", "interval": "", "intervalFactor": 10, "legendFormat": "{{ name }}", "metric": "container_memory_usage_bytes", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Container Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "decimals": 0, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 17}, "id": 73, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ALERTS{alertstate=\"firing\"}) by (alertname)", "format": "time_series", "interval": "30s", "intervalFactor": 1, "legendFormat": "{{ alertname }}", "metric": "container_memory_usage_bytes", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON><PERSON>", "tooltip": {"msResolution": true, "shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 22, "panels": [], "repeat": null, "title": "Prometheus Metrics", "type": "row"}, {"aliasColors": {"Max": "#e24d42", "Open": "#508642"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 25}, "id": 18, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_max_fds{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max", "refId": "A"}, {"expr": "process_open_fds{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Open", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "File Descriptors", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Allocated bytes": "#7EB26D", "Allocated bytes - 1m max": "#BF1B00", "Allocated bytes - 1m min": "#BF1B00", "Allocated bytes - 5m max": "#BF1B00", "Allocated bytes - 5m min": "#BF1B00", "Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833", "RSS": "#447EBC"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "decimals": null, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 25}, "id": 58, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/-/", "fill": 0}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "RSS", "metric": "process_resident_memory_bytes", "refId": "B", "step": 10}, {"expr": "prometheus_local_storage_target_heap_size_bytes{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Target heap size", "metric": "go_memstats_alloc_bytes", "refId": "D", "step": 10}, {"expr": "go_memstats_next_gc_bytes{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Next GC", "metric": "go_memstats_next_gc_bytes", "refId": "C", "step": 10}, {"expr": "go_memstats_alloc_bytes{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Allocated", "metric": "go_memstats_alloc_bytes", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Allocated bytes": "#F9BA8F", "Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833", "RSS": "#890F02"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 25}, "id": 60, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(go_memstats_alloc_bytes_total{job=\"prometheus\"}[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Allocated", "metric": "go_memstats_alloc_bytes", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Allocations", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833", "Time series": "#70dbed"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 32}, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_series", "format": "time_series", "intervalFactor": 2, "legendFormat": "Time series", "metric": "prometheus_local_storage_memory_series", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Time series", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 32}, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_active_appenders", "format": "time_series", "intervalFactor": 2, "legendFormat": "Head Appenders", "metric": "prometheus_local_storage_memory_series", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Active Appenders", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"samples/s": "#e5a8e2"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 32}, "id": 26, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_head_samples_appended_total[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "<PERSON><PERSON>", "metric": "prometheus_local_storage_ingested_samples_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Sam<PERSON> Appended", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833", "To persist": "#9AC48A"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 39}, "id": 28, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/Max.*/", "fill": 0}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_chunks", "format": "time_series", "intervalFactor": 2, "legendFormat": "Chunks", "metric": "prometheus_local_storage_memory_chunks", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Chunks", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 39}, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_head_chunks_created_total[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Created", "metric": "prometheus_local_storage_chunk_ops_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Chunks Created", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833", "Removed": "#e5ac0e"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 39}, "id": 32, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_head_chunks_removed_total[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Removed", "metric": "prometheus_local_storage_chunk_ops_total", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head <PERSON><PERSON> Removed", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max": "#447ebc", "Max chunks": "#052B51", "Max to persist": "#3F6833", "Min": "#447ebc", "Now": "#7eb26d"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 46}, "id": 34, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Max", "fillBelowTo": "Min", "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_head_min_time", "format": "time_series", "intervalFactor": 2, "legendFormat": "Min", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}, {"expr": "time() * 1000", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Now", "refId": "C"}, {"expr": "prometheus_tsdb_head_max_time", "format": "time_series", "intervalFactor": 2, "legendFormat": "Max", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head Time Range", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "dateTimeAsIso", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 46}, "id": 36, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_head_gc_duration_seconds_sum[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "GC Time", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Head GC Time/s", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 46}, "id": 38, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Queue length", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_tsdb_blocks_loaded", "format": "time_series", "intervalFactor": 2, "legendFormat": "Blocks Loaded", "metric": "prometheus_local_storage_indexing_batch_sizes_sum", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Blocks Loaded", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Failed Compactions": "#bf1b00", "Failed Reloads": "#bf1b00", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 53}, "id": 40, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_reloads_total[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Reloads", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "TSDB Reloads", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Failed Compactions": "#bf1b00", "Max chunks": "#052B51", "Max to persist": "#3F6833", "{instance=\"demo.robustperception.io:9090\",job=\"prometheus\"}": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 53}, "id": 44, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_wal_corruptions_total[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "WAL Corruptions", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}, {"expr": "rate(prometheus_tsdb_reloads_failures_total[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Reload Failures", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "B", "step": 10}, {"expr": "rate(prometheus_tsdb_head_series_not_found[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Head Series Not Found", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "C", "step": 10}, {"expr": "rate(prometheus_tsdb_compactions_failed_total[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Compaction Failures", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "D", "step": 10}, {"expr": "rate(prometheus_tsdb_retention_cutoffs_failures_total[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Retention Cutoff Failures", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "E", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "TSDB Problems", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Failed Compactions": "#bf1b00", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 53}, "id": 42, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_wal_fsync_duration_seconds_sum[1m]) / rate(prometheus_tsdb_wal_fsync_duration_seconds_count[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Fsync Latency", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}, {"expr": "rate(prometheus_tsdb_wal_truncate_duration_seconds_sum[1m]) / rate(prometheus_tsdb_wal_truncate_duration_seconds_count[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Truncate Latency", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "WAL Latencies", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Failed Compactions": "#bf1b00", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 60}, "id": 46, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_compactions_total[10m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Compactions", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compactions", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 60}, "id": 48, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_compaction_duration_seconds_sum[10m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Compaction Time", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Compaction Time", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Allocated bytes": "#F9BA8F", "Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833", "RSS": "#890F02"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 60}, "id": 50, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_retention_cutoffs_total[10m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Retention Cutoffs", "metric": "last", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Retention Cutoffs", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 67}, "id": 56, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_tsdb_compaction_chunk_samples_sum[10m]) / rate(prometheus_tsdb_compaction_chunk_samples_count[10m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Chunk Samples", "metric": "prometheus_local_storage_series_chunks_persisted_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "First Compaction, Avg Chunk Samples", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 67}, "id": 10, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_target_interval_length_seconds_count[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ interval }}", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Target Scrapes", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 67}, "id": 11, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "prometheus_target_interval_length_seconds{quantile!=\"0.01\", quantile!=\"0.05\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{quantile}} ({{interval}})", "refId": "A", "step": 2}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Scrape Duration", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "", "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 74}, "id": 62, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_http_request_duration_seconds_count[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{handler}}", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "HTTP requests", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "reqps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "", "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 74}, "id": 64, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_http_request_duration_seconds_sum[1m]) / rate(prometheus_http_request_duration_seconds_count[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{handler}}", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "HTTP request latency", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "", "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 74}, "id": 66, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(prometheus_http_request_duration_seconds_sum[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{handler}}", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Time spent in HTTP requests", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Time spent in each mode, per second", "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 81}, "id": 68, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(prometheus_engine_query_duration_seconds_sum[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{slice}}", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Query engine timings", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 81}, "id": 70, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(prometheus_rule_group_iterations_missed_total[1m])  ", "format": "time_series", "intervalFactor": 2, "legendFormat": "Rule group missed", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "B", "step": 10}, {"expr": "rate(prometheus_rule_evaluation_failures_total[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Rule evals failed", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Rule group evaulation problems", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Chunks": "#1F78C1", "Chunks to persist": "#508642", "Max chunks": "#052B51", "Max to persist": "#3F6833"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "editable": true, "error": false, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 81}, "id": 72, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(prometheus_rule_group_duration_seconds_sum[1m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Rule evaluation duration", "metric": "prometheus_local_storage_memory_chunkdescs", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Evaluation time of rule groups", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": []}, "annotations": {"list": []}, "refresh": "10s", "schemaVersion": 12, "version": 22, "links": [], "gnetId": null}