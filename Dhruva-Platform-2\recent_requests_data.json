=== DHRUVA PLATFORM RECENT MONITORING DATA EXPORT ===
Export Time: Tue May 27 09:40:33 UTC 2025
Time Range: Last 5 minutes
Start Time: 2025-05-27T09:35:33+00:00
End Time: 2025-05-27T09:40:33+00:00

{
  "dhruva_requests_total": {
    "description": "Total HTTP requests to Dhruva Platform endpoints",
    "time_range": "last_5_minutes",
    "data": [
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "7"
    ],
    [
      1748338566,
      "7"
    ],
    [
      1748338581,
      "7"
    ],
    [
      1748338596,
      "7"
    ],
    [
      1748338611,
      "7"
    ],
    [
      1748338626,
      "7"
    ],
    [
      1748338641,
      "7"
    ],
    [
      1748338656,
      "7"
    ],
    [
      1748338671,
      "7"
    ],
    [
      1748338686,
      "7"
    ],
    [
      1748338701,
      "7"
    ],
    [
      1748338716,
      "7"
    ],
    [
      1748338731,
      "7"
    ],
    [
      1748338746,
      "7"
    ],
    [
      1748338761,
      "7"
    ],
    [
      1748338776,
      "7"
    ],
    [
      1748338791,
      "7"
    ],
    [
      1748338806,
      "7"
    ],
    [
      1748338821,
      "7"
    ],
    [
      1748338836,
      "7"
    ],
    [
      1748338851,
      "7"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "5"
    ],
    [
      1748338566,
      "5"
    ],
    [
      1748338581,
      "5"
    ],
    [
      1748338596,
      "5"
    ],
    [
      1748338611,
      "5"
    ],
    [
      1748338626,
      "5"
    ],
    [
      1748338641,
      "5"
    ],
    [
      1748338656,
      "5"
    ],
    [
      1748338671,
      "5"
    ],
    [
      1748338686,
      "5"
    ],
    [
      1748338701,
      "5"
    ],
    [
      1748338716,
      "5"
    ],
    [
      1748338731,
      "5"
    ],
    [
      1748338746,
      "5"
    ],
    [
      1748338761,
      "5"
    ],
    [
      1748338776,
      "5"
    ],
    [
      1748338791,
      "5"
    ],
    [
      1748338806,
      "5"
    ],
    [
      1748338821,
      "5"
    ],
    [
      1748338836,
      "5"
    ],
    [
      1748338851,
      "5"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/favicon.ico",
    "status_code": "404",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/metrics",
    "status_code": "307",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/metrics/",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "3"
    ],
    [
      1748338566,
      "3"
    ],
    [
      1748338581,
      "3"
    ],
    [
      1748338596,
      "3"
    ],
    [
      1748338611,
      "3"
    ],
    [
      1748338626,
      "3"
    ],
    [
      1748338641,
      "3"
    ],
    [
      1748338656,
      "3"
    ],
    [
      1748338671,
      "3"
    ],
    [
      1748338686,
      "3"
    ],
    [
      1748338701,
      "3"
    ],
    [
      1748338716,
      "3"
    ],
    [
      1748338731,
      "3"
    ],
    [
      1748338746,
      "3"
    ],
    [
      1748338761,
      "3"
    ],
    [
      1748338776,
      "3"
    ],
    [
      1748338791,
      "3"
    ],
    [
      1748338806,
      "3"
    ],
    [
      1748338821,
      "3"
    ],
    [
      1748338836,
      "3"
    ],
    [
      1748338851,
      "3"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "OPTIONS",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "3"
    ],
    [
      1748338806,
      "3"
    ],
    [
      1748338821,
      "3"
    ],
    [
      1748338836,
      "3"
    ],
    [
      1748338851,
      "3"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "OPTIONS",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "401",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "401",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "401",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "7"
    ],
    [
      1748338566,
      "7"
    ],
    [
      1748338581,
      "7"
    ],
    [
      1748338596,
      "7"
    ],
    [
      1748338611,
      "7"
    ],
    [
      1748338626,
      "7"
    ],
    [
      1748338641,
      "7"
    ],
    [
      1748338656,
      "7"
    ],
    [
      1748338671,
      "7"
    ],
    [
      1748338686,
      "7"
    ],
    [
      1748338701,
      "7"
    ],
    [
      1748338716,
      "7"
    ],
    [
      1748338731,
      "7"
    ],
    [
      1748338746,
      "7"
    ],
    [
      1748338761,
      "7"
    ],
    [
      1748338776,
      "7"
    ],
    [
      1748338791,
      "7"
    ],
    [
      1748338806,
      "7"
    ],
    [
      1748338821,
      "7"
    ],
    [
      1748338836,
      "7"
    ],
    [
      1748338851,
      "7"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "5"
    ],
    [
      1748338566,
      "5"
    ],
    [
      1748338581,
      "5"
    ],
    [
      1748338596,
      "5"
    ],
    [
      1748338611,
      "5"
    ],
    [
      1748338626,
      "5"
    ],
    [
      1748338641,
      "5"
    ],
    [
      1748338656,
      "5"
    ],
    [
      1748338671,
      "5"
    ],
    [
      1748338686,
      "5"
    ],
    [
      1748338701,
      "5"
    ],
    [
      1748338716,
      "5"
    ],
    [
      1748338731,
      "5"
    ],
    [
      1748338746,
      "5"
    ],
    [
      1748338761,
      "5"
    ],
    [
      1748338776,
      "5"
    ],
    [
      1748338791,
      "5"
    ],
    [
      1748338806,
      "5"
    ],
    [
      1748338821,
      "5"
    ],
    [
      1748338836,
      "5"
    ],
    [
      1748338851,
      "5"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/favicon.ico",
    "status_code": "404",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/metrics",
    "status_code": "307",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/metrics/",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "3"
    ],
    [
      1748338566,
      "3"
    ],
    [
      1748338581,
      "3"
    ],
    [
      1748338596,
      "3"
    ],
    [
      1748338611,
      "3"
    ],
    [
      1748338626,
      "3"
    ],
    [
      1748338641,
      "3"
    ],
    [
      1748338656,
      "3"
    ],
    [
      1748338671,
      "3"
    ],
    [
      1748338686,
      "3"
    ],
    [
      1748338701,
      "3"
    ],
    [
      1748338716,
      "3"
    ],
    [
      1748338731,
      "3"
    ],
    [
      1748338746,
      "3"
    ],
    [
      1748338761,
      "3"
    ],
    [
      1748338776,
      "3"
    ],
    [
      1748338791,
      "3"
    ],
    [
      1748338806,
      "3"
    ],
    [
      1748338821,
      "3"
    ],
    [
      1748338836,
      "3"
    ],
    [
      1748338851,
      "3"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "OPTIONS",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "3"
    ],
    [
      1748338806,
      "3"
    ],
    [
      1748338821,
      "3"
    ],
    [
      1748338836,
      "3"
    ],
    [
      1748338851,
      "3"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "OPTIONS",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "401",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "401",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "401",
    "user_id": "None"
  },
  "values": [
    [
      1748338551,
      "2"
    ],
    [
      1748338566,
      "2"
    ],
    [
      1748338581,
      "2"
    ],
    [
      1748338596,
      "2"
    ],
    [
      1748338611,
      "2"
    ],
    [
      1748338626,
      "2"
    ],
    [
      1748338641,
      "2"
    ],
    [
      1748338656,
      "2"
    ],
    [
      1748338671,
      "2"
    ],
    [
      1748338686,
      "2"
    ],
    [
      1748338701,
      "2"
    ],
    [
      1748338716,
      "2"
    ],
    [
      1748338731,
      "2"
    ],
    [
      1748338746,
      "2"
    ],
    [
      1748338761,
      "2"
    ],
    [
      1748338776,
      "2"
    ],
    [
      1748338791,
      "2"
    ],
    [
      1748338806,
      "2"
    ],
    [
      1748338821,
      "2"
    ],
    [
      1748338836,
      "2"
    ],
    [
      1748338851,
      "2"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "4"
    ],
    [
      1748338566,
      "4"
    ],
    [
      1748338581,
      "4"
    ],
    [
      1748338596,
      "4"
    ],
    [
      1748338611,
      "4"
    ],
    [
      1748338626,
      "4"
    ],
    [
      1748338641,
      "4"
    ],
    [
      1748338656,
      "4"
    ],
    [
      1748338671,
      "4"
    ],
    [
      1748338686,
      "4"
    ],
    [
      1748338701,
      "4"
    ],
    [
      1748338716,
      "4"
    ],
    [
      1748338731,
      "4"
    ],
    [
      1748338746,
      "4"
    ],
    [
      1748338761,
      "4"
    ],
    [
      1748338776,
      "4"
    ],
    [
      1748338791,
      "4"
    ],
    [
      1748338806,
      "4"
    ],
    [
      1748338821,
      "4"
    ],
    [
      1748338836,
      "4"
    ],
    [
      1748338851,
      "4"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "3"
    ],
    [
      1748338566,
      "3"
    ],
    [
      1748338581,
      "3"
    ],
    [
      1748338596,
      "3"
    ],
    [
      1748338611,
      "3"
    ],
    [
      1748338626,
      "3"
    ],
    [
      1748338641,
      "3"
    ],
    [
      1748338656,
      "3"
    ],
    [
      1748338671,
      "3"
    ],
    [
      1748338686,
      "3"
    ],
    [
      1748338701,
      "3"
    ],
    [
      1748338716,
      "3"
    ],
    [
      1748338731,
      "3"
    ],
    [
      1748338746,
      "3"
    ],
    [
      1748338761,
      "3"
    ],
    [
      1748338776,
      "3"
    ],
    [
      1748338791,
      "3"
    ],
    [
      1748338806,
      "3"
    ],
    [
      1748338821,
      "3"
    ],
    [
      1748338836,
      "3"
    ],
    [
      1748338851,
      "3"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "4"
    ],
    [
      1748338566,
      "4"
    ],
    [
      1748338581,
      "4"
    ],
    [
      1748338596,
      "4"
    ],
    [
      1748338611,
      "4"
    ],
    [
      1748338626,
      "4"
    ],
    [
      1748338641,
      "4"
    ],
    [
      1748338656,
      "4"
    ],
    [
      1748338671,
      "4"
    ],
    [
      1748338686,
      "4"
    ],
    [
      1748338701,
      "4"
    ],
    [
      1748338716,
      "4"
    ],
    [
      1748338731,
      "4"
    ],
    [
      1748338746,
      "4"
    ],
    [
      1748338761,
      "4"
    ],
    [
      1748338776,
      "4"
    ],
    [
      1748338791,
      "4"
    ],
    [
      1748338806,
      "4"
    ],
    [
      1748338821,
      "4"
    ],
    [
      1748338836,
      "4"
    ],
    [
      1748338851,
      "4"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "3"
    ],
    [
      1748338566,
      "3"
    ],
    [
      1748338581,
      "3"
    ],
    [
      1748338596,
      "3"
    ],
    [
      1748338611,
      "3"
    ],
    [
      1748338626,
      "3"
    ],
    [
      1748338641,
      "3"
    ],
    [
      1748338656,
      "3"
    ],
    [
      1748338671,
      "3"
    ],
    [
      1748338686,
      "3"
    ],
    [
      1748338701,
      "3"
    ],
    [
      1748338716,
      "3"
    ],
    [
      1748338731,
      "3"
    ],
    [
      1748338746,
      "3"
    ],
    [
      1748338761,
      "3"
    ],
    [
      1748338776,
      "3"
    ],
    [
      1748338791,
      "3"
    ],
    [
      1748338806,
      "3"
    ],
    [
      1748338821,
      "3"
    ],
    [
      1748338836,
      "3"
    ],
    [
      1748338851,
      "3"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/services/details/list_models",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "13"
    ],
    [
      1748338566,
      "13"
    ],
    [
      1748338581,
      "13"
    ],
    [
      1748338596,
      "13"
    ],
    [
      1748338611,
      "13"
    ],
    [
      1748338626,
      "13"
    ],
    [
      1748338641,
      "13"
    ],
    [
      1748338656,
      "13"
    ],
    [
      1748338671,
      "13"
    ],
    [
      1748338686,
      "13"
    ],
    [
      1748338701,
      "13"
    ],
    [
      1748338716,
      "14"
    ],
    [
      1748338731,
      "14"
    ],
    [
      1748338746,
      "14"
    ],
    [
      1748338761,
      "14"
    ],
    [
      1748338776,
      "14"
    ],
    [
      1748338791,
      "14"
    ],
    [
      1748338806,
      "14"
    ],
    [
      1748338821,
      "14"
    ],
    [
      1748338836,
      "14"
    ],
    [
      1748338851,
      "14"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/services/details/list_services",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "13"
    ],
    [
      1748338566,
      "13"
    ],
    [
      1748338581,
      "13"
    ],
    [
      1748338596,
      "13"
    ],
    [
      1748338611,
      "13"
    ],
    [
      1748338626,
      "13"
    ],
    [
      1748338641,
      "13"
    ],
    [
      1748338656,
      "13"
    ],
    [
      1748338671,
      "13"
    ],
    [
      1748338686,
      "13"
    ],
    [
      1748338701,
      "13"
    ],
    [
      1748338716,
      "14"
    ],
    [
      1748338731,
      "14"
    ],
    [
      1748338746,
      "14"
    ],
    [
      1748338761,
      "14"
    ],
    [
      1748338776,
      "14"
    ],
    [
      1748338791,
      "14"
    ],
    [
      1748338806,
      "14"
    ],
    [
      1748338821,
      "14"
    ],
    [
      1748338836,
      "14"
    ],
    [
      1748338851,
      "14"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/services/details/list_models",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "13"
    ],
    [
      1748338566,
      "13"
    ],
    [
      1748338581,
      "13"
    ],
    [
      1748338596,
      "13"
    ],
    [
      1748338611,
      "13"
    ],
    [
      1748338626,
      "13"
    ],
    [
      1748338641,
      "13"
    ],
    [
      1748338656,
      "13"
    ],
    [
      1748338671,
      "13"
    ],
    [
      1748338686,
      "13"
    ],
    [
      1748338701,
      "13"
    ],
    [
      1748338716,
      "13"
    ],
    [
      1748338731,
      "14"
    ],
    [
      1748338746,
      "14"
    ],
    [
      1748338761,
      "14"
    ],
    [
      1748338776,
      "14"
    ],
    [
      1748338791,
      "14"
    ],
    [
      1748338806,
      "14"
    ],
    [
      1748338821,
      "14"
    ],
    [
      1748338836,
      "14"
    ],
    [
      1748338851,
      "14"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/services/details/list_services",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "13"
    ],
    [
      1748338566,
      "13"
    ],
    [
      1748338581,
      "13"
    ],
    [
      1748338596,
      "13"
    ],
    [
      1748338611,
      "13"
    ],
    [
      1748338626,
      "13"
    ],
    [
      1748338641,
      "13"
    ],
    [
      1748338656,
      "13"
    ],
    [
      1748338671,
      "13"
    ],
    [
      1748338686,
      "13"
    ],
    [
      1748338701,
      "13"
    ],
    [
      1748338716,
      "13"
    ],
    [
      1748338731,
      "14"
    ],
    [
      1748338746,
      "14"
    ],
    [
      1748338761,
      "14"
    ],
    [
      1748338776,
      "14"
    ],
    [
      1748338791,
      "14"
    ],
    [
      1748338806,
      "14"
    ],
    [
      1748338821,
      "14"
    ],
    [
      1748338836,
      "14"
    ],
    [
      1748338851,
      "14"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "39"
    ],
    [
      1748338566,
      "39"
    ],
    [
      1748338581,
      "39"
    ],
    [
      1748338596,
      "39"
    ],
    [
      1748338611,
      "39"
    ],
    [
      1748338626,
      "39"
    ],
    [
      1748338641,
      "39"
    ],
    [
      1748338656,
      "39"
    ],
    [
      1748338671,
      "39"
    ],
    [
      1748338686,
      "39"
    ],
    [
      1748338701,
      "39"
    ],
    [
      1748338716,
      "39"
    ],
    [
      1748338731,
      "39"
    ],
    [
      1748338746,
      "39"
    ],
    [
      1748338761,
      "39"
    ],
    [
      1748338776,
      "39"
    ],
    [
      1748338791,
      "39"
    ],
    [
      1748338806,
      "39"
    ],
    [
      1748338821,
      "39"
    ],
    [
      1748338836,
      "39"
    ],
    [
      1748338851,
      "39"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "1"
    ],
    [
      1748338566,
      "1"
    ],
    [
      1748338581,
      "1"
    ],
    [
      1748338596,
      "1"
    ],
    [
      1748338611,
      "1"
    ],
    [
      1748338626,
      "1"
    ],
    [
      1748338641,
      "1"
    ],
    [
      1748338656,
      "1"
    ],
    [
      1748338671,
      "1"
    ],
    [
      1748338686,
      "1"
    ],
    [
      1748338701,
      "1"
    ],
    [
      1748338716,
      "1"
    ],
    [
      1748338731,
      "1"
    ],
    [
      1748338746,
      "1"
    ],
    [
      1748338761,
      "1"
    ],
    [
      1748338776,
      "1"
    ],
    [
      1748338791,
      "1"
    ],
    [
      1748338806,
      "1"
    ],
    [
      1748338821,
      "1"
    ],
    [
      1748338836,
      "1"
    ],
    [
      1748338851,
      "1"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "134"
    ],
    [
      1748338566,
      "134"
    ],
    [
      1748338581,
      "134"
    ],
    [
      1748338596,
      "134"
    ],
    [
      1748338611,
      "134"
    ],
    [
      1748338626,
      "134"
    ],
    [
      1748338641,
      "134"
    ],
    [
      1748338656,
      "134"
    ],
    [
      1748338671,
      "134"
    ],
    [
      1748338686,
      "134"
    ],
    [
      1748338701,
      "134"
    ],
    [
      1748338716,
      "134"
    ],
    [
      1748338731,
      "134"
    ],
    [
      1748338746,
      "134"
    ],
    [
      1748338761,
      "134"
    ],
    [
      1748338776,
      "134"
    ],
    [
      1748338791,
      "134"
    ],
    [
      1748338806,
      "134"
    ],
    [
      1748338821,
      "134"
    ],
    [
      1748338836,
      "134"
    ],
    [
      1748338851,
      "134"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "40"
    ],
    [
      1748338566,
      "40"
    ],
    [
      1748338581,
      "40"
    ],
    [
      1748338596,
      "40"
    ],
    [
      1748338611,
      "40"
    ],
    [
      1748338626,
      "40"
    ],
    [
      1748338641,
      "40"
    ],
    [
      1748338656,
      "40"
    ],
    [
      1748338671,
      "40"
    ],
    [
      1748338686,
      "40"
    ],
    [
      1748338701,
      "40"
    ],
    [
      1748338716,
      "40"
    ],
    [
      1748338731,
      "40"
    ],
    [
      1748338746,
      "40"
    ],
    [
      1748338761,
      "40"
    ],
    [
      1748338776,
      "40"
    ],
    [
      1748338791,
      "40"
    ],
    [
      1748338806,
      "40"
    ],
    [
      1748338821,
      "40"
    ],
    [
      1748338836,
      "40"
    ],
    [
      1748338851,
      "40"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "39"
    ],
    [
      1748338566,
      "39"
    ],
    [
      1748338581,
      "39"
    ],
    [
      1748338596,
      "39"
    ],
    [
      1748338611,
      "39"
    ],
    [
      1748338626,
      "39"
    ],
    [
      1748338641,
      "39"
    ],
    [
      1748338656,
      "39"
    ],
    [
      1748338671,
      "39"
    ],
    [
      1748338686,
      "39"
    ],
    [
      1748338701,
      "39"
    ],
    [
      1748338716,
      "39"
    ],
    [
      1748338731,
      "39"
    ],
    [
      1748338746,
      "39"
    ],
    [
      1748338761,
      "39"
    ],
    [
      1748338776,
      "39"
    ],
    [
      1748338791,
      "39"
    ],
    [
      1748338806,
      "39"
    ],
    [
      1748338821,
      "39"
    ],
    [
      1748338836,
      "39"
    ],
    [
      1748338851,
      "39"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "1"
    ],
    [
      1748338566,
      "1"
    ],
    [
      1748338581,
      "1"
    ],
    [
      1748338596,
      "1"
    ],
    [
      1748338611,
      "1"
    ],
    [
      1748338626,
      "1"
    ],
    [
      1748338641,
      "1"
    ],
    [
      1748338656,
      "1"
    ],
    [
      1748338671,
      "1"
    ],
    [
      1748338686,
      "1"
    ],
    [
      1748338701,
      "1"
    ],
    [
      1748338716,
      "1"
    ],
    [
      1748338731,
      "1"
    ],
    [
      1748338746,
      "1"
    ],
    [
      1748338761,
      "1"
    ],
    [
      1748338776,
      "1"
    ],
    [
      1748338791,
      "1"
    ],
    [
      1748338806,
      "1"
    ],
    [
      1748338821,
      "1"
    ],
    [
      1748338836,
      "1"
    ],
    [
      1748338851,
      "1"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "134"
    ],
    [
      1748338566,
      "134"
    ],
    [
      1748338581,
      "134"
    ],
    [
      1748338596,
      "134"
    ],
    [
      1748338611,
      "134"
    ],
    [
      1748338626,
      "134"
    ],
    [
      1748338641,
      "134"
    ],
    [
      1748338656,
      "134"
    ],
    [
      1748338671,
      "134"
    ],
    [
      1748338686,
      "134"
    ],
    [
      1748338701,
      "134"
    ],
    [
      1748338716,
      "134"
    ],
    [
      1748338731,
      "134"
    ],
    [
      1748338746,
      "134"
    ],
    [
      1748338761,
      "134"
    ],
    [
      1748338776,
      "134"
    ],
    [
      1748338791,
      "134"
    ],
    [
      1748338806,
      "134"
    ],
    [
      1748338821,
      "134"
    ],
    [
      1748338836,
      "134"
    ],
    [
      1748338851,
      "134"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "40"
    ],
    [
      1748338566,
      "40"
    ],
    [
      1748338581,
      "40"
    ],
    [
      1748338596,
      "40"
    ],
    [
      1748338611,
      "40"
    ],
    [
      1748338626,
      "40"
    ],
    [
      1748338641,
      "40"
    ],
    [
      1748338656,
      "40"
    ],
    [
      1748338671,
      "40"
    ],
    [
      1748338686,
      "40"
    ],
    [
      1748338701,
      "40"
    ],
    [
      1748338716,
      "40"
    ],
    [
      1748338731,
      "40"
    ],
    [
      1748338746,
      "40"
    ],
    [
      1748338761,
      "40"
    ],
    [
      1748338776,
      "40"
    ],
    [
      1748338791,
      "40"
    ],
    [
      1748338806,
      "40"
    ],
    [
      1748338821,
      "40"
    ],
    [
      1748338836,
      "40"
    ],
    [
      1748338851,
      "40"
    ]
  ]
}
    ]
  },
  "dhruva_request_duration_seconds": {
    "description": "HTTP request duration metrics (histogram buckets and summaries)",
    "time_range": "last_5_minutes",
    "data": [
    ]
  },
  "dhruva_inference_request_total": {
    "description": "Total inference requests by service, task type, and language",
    "time_range": "last_5_minutes",
    "data": [
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "default",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictrans--gpu-t4",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "source_language": "en",
    "target_language": "hi",
    "task_type": "translation",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "7"
    ],
    [
      1748338566,
      "7"
    ],
    [
      1748338581,
      "7"
    ],
    [
      1748338596,
      "7"
    ],
    [
      1748338611,
      "7"
    ],
    [
      1748338626,
      "7"
    ],
    [
      1748338641,
      "7"
    ],
    [
      1748338656,
      "7"
    ],
    [
      1748338671,
      "7"
    ],
    [
      1748338686,
      "7"
    ],
    [
      1748338701,
      "7"
    ],
    [
      1748338716,
      "7"
    ],
    [
      1748338731,
      "7"
    ],
    [
      1748338746,
      "7"
    ],
    [
      1748338761,
      "7"
    ],
    [
      1748338776,
      "7"
    ],
    [
      1748338791,
      "7"
    ],
    [
      1748338806,
      "7"
    ],
    [
      1748338821,
      "7"
    ],
    [
      1748338836,
      "7"
    ],
    [
      1748338851,
      "7"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "default",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictrans--gpu-t4",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "source_language": "en",
    "target_language": "hi",
    "task_type": "translation",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "7"
    ],
    [
      1748338566,
      "7"
    ],
    [
      1748338581,
      "7"
    ],
    [
      1748338596,
      "7"
    ],
    [
      1748338611,
      "7"
    ],
    [
      1748338626,
      "7"
    ],
    [
      1748338641,
      "7"
    ],
    [
      1748338656,
      "7"
    ],
    [
      1748338671,
      "7"
    ],
    [
      1748338686,
      "7"
    ],
    [
      1748338701,
      "7"
    ],
    [
      1748338716,
      "7"
    ],
    [
      1748338731,
      "7"
    ],
    [
      1748338746,
      "7"
    ],
    [
      1748338761,
      "7"
    ],
    [
      1748338776,
      "7"
    ],
    [
      1748338791,
      "7"
    ],
    [
      1748338806,
      "7"
    ],
    [
      1748338821,
      "7"
    ],
    [
      1748338836,
      "7"
    ],
    [
      1748338851,
      "7"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "manapi",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictasr",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "source_language": "hi",
    "target_language": "None",
    "task_type": "asr",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "40"
    ],
    [
      1748338566,
      "40"
    ],
    [
      1748338581,
      "40"
    ],
    [
      1748338596,
      "40"
    ],
    [
      1748338611,
      "40"
    ],
    [
      1748338626,
      "40"
    ],
    [
      1748338641,
      "40"
    ],
    [
      1748338656,
      "40"
    ],
    [
      1748338671,
      "40"
    ],
    [
      1748338686,
      "40"
    ],
    [
      1748338701,
      "40"
    ],
    [
      1748338716,
      "40"
    ],
    [
      1748338731,
      "40"
    ],
    [
      1748338746,
      "40"
    ],
    [
      1748338761,
      "40"
    ],
    [
      1748338776,
      "40"
    ],
    [
      1748338791,
      "40"
    ],
    [
      1748338806,
      "40"
    ],
    [
      1748338821,
      "40"
    ],
    [
      1748338836,
      "40"
    ],
    [
      1748338851,
      "40"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "manapi",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictasr",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "source_language": "hi",
    "target_language": "None",
    "task_type": "asr",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "40"
    ],
    [
      1748338566,
      "40"
    ],
    [
      1748338581,
      "40"
    ],
    [
      1748338596,
      "40"
    ],
    [
      1748338611,
      "40"
    ],
    [
      1748338626,
      "40"
    ],
    [
      1748338641,
      "40"
    ],
    [
      1748338656,
      "40"
    ],
    [
      1748338671,
      "40"
    ],
    [
      1748338686,
      "40"
    ],
    [
      1748338701,
      "40"
    ],
    [
      1748338716,
      "40"
    ],
    [
      1748338731,
      "40"
    ],
    [
      1748338746,
      "40"
    ],
    [
      1748338761,
      "40"
    ],
    [
      1748338776,
      "40"
    ],
    [
      1748338791,
      "40"
    ],
    [
      1748338806,
      "40"
    ],
    [
      1748338821,
      "40"
    ],
    [
      1748338836,
      "40"
    ],
    [
      1748338851,
      "40"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "manapi",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictrans--gpu-t4",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "source_language": "hi",
    "target_language": "en",
    "task_type": "translation",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "134"
    ],
    [
      1748338566,
      "134"
    ],
    [
      1748338581,
      "134"
    ],
    [
      1748338596,
      "134"
    ],
    [
      1748338611,
      "134"
    ],
    [
      1748338626,
      "134"
    ],
    [
      1748338641,
      "134"
    ],
    [
      1748338656,
      "134"
    ],
    [
      1748338671,
      "134"
    ],
    [
      1748338686,
      "134"
    ],
    [
      1748338701,
      "134"
    ],
    [
      1748338716,
      "134"
    ],
    [
      1748338731,
      "134"
    ],
    [
      1748338746,
      "134"
    ],
    [
      1748338761,
      "134"
    ],
    [
      1748338776,
      "134"
    ],
    [
      1748338791,
      "134"
    ],
    [
      1748338806,
      "134"
    ],
    [
      1748338821,
      "134"
    ],
    [
      1748338836,
      "134"
    ],
    [
      1748338851,
      "134"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "manapi",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictrans--gpu-t4",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "source_language": "hi",
    "target_language": "en",
    "task_type": "translation",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "134"
    ],
    [
      1748338566,
      "134"
    ],
    [
      1748338581,
      "134"
    ],
    [
      1748338596,
      "134"
    ],
    [
      1748338611,
      "134"
    ],
    [
      1748338626,
      "134"
    ],
    [
      1748338641,
      "134"
    ],
    [
      1748338656,
      "134"
    ],
    [
      1748338671,
      "134"
    ],
    [
      1748338686,
      "134"
    ],
    [
      1748338701,
      "134"
    ],
    [
      1748338716,
      "134"
    ],
    [
      1748338731,
      "134"
    ],
    [
      1748338746,
      "134"
    ],
    [
      1748338761,
      "134"
    ],
    [
      1748338776,
      "134"
    ],
    [
      1748338791,
      "134"
    ],
    [
      1748338806,
      "134"
    ],
    [
      1748338821,
      "134"
    ],
    [
      1748338836,
      "134"
    ],
    [
      1748338851,
      "134"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "manapi",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictts--gpu-t4",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "source_language": "hi",
    "target_language": "None",
    "task_type": "tts",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "40"
    ],
    [
      1748338566,
      "40"
    ],
    [
      1748338581,
      "40"
    ],
    [
      1748338596,
      "40"
    ],
    [
      1748338611,
      "40"
    ],
    [
      1748338626,
      "40"
    ],
    [
      1748338641,
      "40"
    ],
    [
      1748338656,
      "40"
    ],
    [
      1748338671,
      "40"
    ],
    [
      1748338686,
      "40"
    ],
    [
      1748338701,
      "40"
    ],
    [
      1748338716,
      "40"
    ],
    [
      1748338731,
      "40"
    ],
    [
      1748338746,
      "40"
    ],
    [
      1748338761,
      "40"
    ],
    [
      1748338776,
      "40"
    ],
    [
      1748338791,
      "40"
    ],
    [
      1748338806,
      "40"
    ],
    [
      1748338821,
      "40"
    ],
    [
      1748338836,
      "40"
    ],
    [
      1748338851,
      "40"
    ]
  ]
}
{
  "metric": {
    "__name__": "dhruva_inference_request_total",
    "api_key_name": "manapi",
    "exported_job": "metrics_push",
    "inference_service": "ai4bharat/indictts--gpu-t4",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "source_language": "hi",
    "target_language": "None",
    "task_type": "tts",
    "user_id": "6800940478d4d09365d861e1"
  },
  "values": [
    [
      1748338551,
      "40"
    ],
    [
      1748338566,
      "40"
    ],
    [
      1748338581,
      "40"
    ],
    [
      1748338596,
      "40"
    ],
    [
      1748338611,
      "40"
    ],
    [
      1748338626,
      "40"
    ],
    [
      1748338641,
      "40"
    ],
    [
      1748338656,
      "40"
    ],
    [
      1748338671,
      "40"
    ],
    [
      1748338686,
      "40"
    ],
    [
      1748338701,
      "40"
    ],
    [
      1748338716,
      "40"
    ],
    [
      1748338731,
      "40"
    ],
    [
      1748338746,
      "40"
    ],
    [
      1748338761,
      "40"
    ],
    [
      1748338776,
      "40"
    ],
    [
      1748338791,
      "40"
    ],
    [
      1748338806,
      "40"
    ],
    [
      1748338821,
      "40"
    ],
    [
      1748338836,
      "40"
    ],
    [
      1748338851,
      "40"
    ]
  ]
}
    ]
  },
  "dhruva_inference_request_duration_seconds": {
    "description": "Inference request duration metrics by service and task type",
    "time_range": "last_5_minutes",
    "data": [
    ]
  },
  "current_snapshot": {
    "description": "Current point-in-time values for immediate analysis",
    "timestamp": "2025-05-27T09:44:51+00:00",
    "dhruva_requests_total_current": [
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/services/details/list_models",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "16",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/services/details/list_services",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "16",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/services/details/list_models",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "16",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "heartbeat",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/services/details/list_services",
    "status_code": "403",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "16",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "6",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "OPTIONS",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "3",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "6",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "OPTIONS",
    "path": "/auth/user",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "3",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "7",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "7",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/metrics",
    "status_code": "307",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/metrics",
    "status_code": "307",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/metrics/",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "3",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/metrics/",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "3",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "OPTIONS",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "3",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "OPTIONS",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "3",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "4",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "default",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "4",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "GET",
    "path": "/favicon.ico",
    "status_code": "404",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "GET",
    "path": "/favicon.ico",
    "status_code": "404",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "401",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "401",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "401",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "401",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "401",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "None",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "401",
    "user_id": "None"
  },
  "value": "2",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "134",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "40",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/translation",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "134",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/tts",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "40",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "1",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "500",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "1",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prometheus",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "39",
  "timestamp": 1748339110.733
}
{
  "metric": {
    "__name__": "dhruva_requests_total",
    "api_key_name": "manapi",
    "app_name": "dhruva",
    "exported_job": "metrics_push",
    "instance": "dhruva-platform-pushgateway:9091",
    "job": "prom-aggregation-gateway",
    "method": "POST",
    "path": "/services/inference/asr",
    "status_code": "200",
    "user_id": "6800940478d4d09365d861e1"
  },
  "value": "39",
  "timestamp": 1748339110.733
}
    ]
  }
}
