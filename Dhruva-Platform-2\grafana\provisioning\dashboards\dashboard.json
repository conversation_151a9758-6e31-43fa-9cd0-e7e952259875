{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 1, "id": 28, "iteration": 1640698809177, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 36, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (service, path_template) (rate(starlette_requests_total{namespace=\"$namespace\", service=\"$service\", path_template=~\"/v1/.*\"}[$__interval_sx4]))", "interval": "", "legendFormat": "{{path_template}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:321", "format": "reqps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:322", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bool"}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 12, "y": 0}, "id": 46, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "python_info{namespace=\"$namespace\", service=\"$service\"}", "format": "table", "interval": "", "legendFormat": "{{implementation}} {{ version }}", "refId": "A"}], "title": "CPython 3.9.6", "transformations": [], "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 5, "w": 9, "x": 15, "y": 0}, "id": 53, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "process_start_time_seconds{namespace=\"$namespace\", service=\"$service\"} * 1000", "interval": "", "legendFormat": "{{ pod }}", "refId": "A"}], "title": "Pod start time ", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 9, "x": 15, "y": 5}, "id": 38, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "sum(process_open_fds{namespace=\"$namespace\", service=\"$service\"}) by (pod)", "format": "time_series", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Number of open file descriptors", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Total user and system CPU time spent in seconds", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "avg by (pod) (rate(process_cpu_seconds_total{namespace=\"$namespace\", service=\"$service\"}[$__interval_sx4])) * 100", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "CPU usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 9, "x": 15, "y": 10}, "id": 55, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "process_max_fds{namespace=\"$namespace\", service=\"$service\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "title": "Maximum number of open file descriptors", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 15}, "id": 42, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum by (service, path_template, exception_type) (rate(starlette_exceptions_total{namespace=\"$namespace\", service=\"$service\", path_template=~\"/v1/.*\"}[$__interval_sx4]))", "interval": "", "legendFormat": "{{path_template}} {{ exception_type }}", "refId": "A"}], "title": "Total count of exceptions raised by path and exception type", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fill": 10, "fillGradient": 3, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(starlette_responses_total{namespace=\"$namespace\", status_code=~\"5..\"}[$__interval_sx4])) by (service, path_template) / sum(rate(starlette_responses_total{namespace=\"$namespace\"}[$__interval_sx4])) by (service, path_template) *100", "interval": "", "legendFormat": "{{path_template}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:165", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:166", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 51, "panels": [], "title": "Memory", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 48, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "process_virtual_memory_bytes{namespace=\"$namespace\", service=\"$service\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Virtual memory size in bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 49, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "process_resident_memory_bytes{namespace=\"$namespace\", service=\"$service\"}", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Resident memory size in bytes", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 30, "panels": [], "title": "Requests", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 34}, "hiddenSeries": false, "id": 40, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(starlette_requests_processing_time_seconds_bucket{namespace=\"$namespace\", service=\"$service\"}[5m])) by (le))", "interval": "", "legendFormat": "0.95", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(starlette_requests_processing_time_seconds_bucket{namespace=\"$namespace\", service=\"$service\"}[5m])) by (le))", "hide": false, "interval": "", "legendFormat": "0.9", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(starlette_requests_processing_time_seconds_bucket{namespace=\"$namespace\", service=\"$service\"}[5m])) by (le))", "hide": false, "interval": "", "legendFormat": "0.5", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.1, sum(rate(starlette_requests_processing_time_seconds_bucket{namespace=\"$namespace\", service=\"$service\"}[5m])) by (le))", "hide": false, "interval": "", "legendFormat": "0.1", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Histogram of requests processing time by path (in seconds)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:969", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:970", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "hiddenSeries": false, "id": 44, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "starlette_requests_in_progress{namespace=\"$namespace\", service=\"$service\", path_template=~\"/v1/.*\"}", "hide": false, "interval": "", "legendFormat": "{{ path_template}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests currently being processed", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1015", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1016", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 42}, "id": 26, "panels": [], "title": "Responses", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Total count of responses by method, path and status codes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 43}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "sum(rate(starlette_responses_total{namespace=\"$namespace\", service=\"$service\", status_code=~\"2..\", path_template=~\"/v1/.*\"}[$__interval_sx4])) by (service, status_code)", "hide": false, "interval": "", "legendFormat": "{{ status_code }}", "refId": "C"}], "title": "Responses total 2xx", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Total count of responses by method, path and status codes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 43}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "sum(rate(starlette_responses_total{namespace=\"$namespace\", service=\"$service\", status_code=~\"4..\", path_template=~\"/v1/.*\"}[$__interval_sx4])) by (service, status_code)", "hide": false, "interval": "", "legendFormat": "{{ status_code }}", "refId": "C"}], "title": "Responses total 4xx", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Total count of responses by method, path and status codes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 43}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "sum(rate(starlette_responses_total{namespace=\"$namespace\", service=\"$service\", status_code=~\"5..\", path_template=~\"/v1/.*\"}[$__interval_sx4])) by (service, status_code)", "hide": false, "interval": "", "legendFormat": "{{ status_code }}", "refId": "C"}], "title": "Responses total 5xx", "transformations": [], "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 52}, "id": 24, "panels": [], "title": "GC", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Objects collected during gc", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 53}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "sum(rate(python_gc_objects_collected_total{namespace=\"$namespace\", service=\"$service\",}[$__interval_sx4])) by (service)", "interval": "", "legendFormat": "{{ service }}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "GC objects collected total", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Uncollectable object found during GC", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 53}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(rate(python_gc_objects_uncollectable_total{namespace=\"$namespace\", service=\"$service\",}[$__interval_sx4])) by (service)", "interval": "", "legendFormat": "{{ service }}", "refId": "A"}], "title": "GC objects uncollectable total", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "kdjfS_R7c"}, "description": "Number of times this generation was collected", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 53}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(python_gc_collections_total{namespace=\"$namespace\", service=\"$service\"}[$__interval_sx4])) by (service)", "interval": "", "legendFormat": "{{ service  }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC collections total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:789", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:790", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "30s", "schemaVersion": 32, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "dev", "value": "dev"}, "datasource": null, "definition": "label_values(namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "dev|sandbox|preproduction|production|qa|staging", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": null, "current": {"selected": false, "text": "mytech-api", "value": "mytech-api"}, "datasource": null, "definition": "label_values(service)", "description": "service", "error": null, "hide": 0, "includeAll": false, "label": "service", "multi": false, "name": "service", "options": [], "query": {"query": "label_values(service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "mytech-api|file-saver", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "FastApi dashboard", "uid": "Zj4zOgA7x", "version": 5}