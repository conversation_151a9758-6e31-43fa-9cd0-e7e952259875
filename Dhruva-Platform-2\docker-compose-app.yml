#made changes to get this file working for the first time. 

services:
  server:
    image: dhruva-platform-server:latest-pg15
    container_name: dhruva-platform-server
    build:
      context: ./server
      dockerfile: Dockerfile
    env_file: .env
    command: python3 -m uvicorn main:app --host 0.0.0.0 --port 8000
    depends_on:
      redis:
        condition: service_healthy
      app_db:
        condition: service_healthy
      rabbitmq_server:
        condition: service_started
      timescaledb:
        condition: service_healthy
    ports:
      - "8000:8000"
    networks:
      - dhruva-network

  worker:
    image: dhruva-platform-worker:latest
    container_name: dhruva-platform-worker
    build:
      context: ./server
      dockerfile: Dockerfile
    env_file: .env
    command: >
      celery -A celery_backend.celery_app worker --loglevel=info
    depends_on:
      redis:
        condition: service_healthy
      app_db:
        condition: service_healthy
      rabbitmq_server:
        condition: service_started
      timescaledb:
        condition: service_healthy
    networks:
      - dhruva-network

  flower:
    build:
      context: .
      dockerfile: docker/flower/Dockerfile
    container_name: dhruva-platform-flower
    command: >
      sh -c "
        echo 'Waiting for RabbitMQ...' &&
        /wait-for-it.sh rabbitmq_server:5672 -t 60 -s -- echo 'RabbitMQ is up' &&
        echo 'Starting Flower...' &&
        celery flower --broker=amqp://${RABBITMQ_DEFAULT_USER}:${RABBITMQ_DEFAULT_PASS}@rabbitmq_server:5672/${RABBITMQ_DEFAULT_VHOST}
      "
    environment:
      - FLOWER_ADDRESS=0.0.0.0
      - FLOWER_PORT=5555
      - FLOWER_LOGGING=DEBUG
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
      - RABBITMQ_DEFAULT_VHOST=dhruva_host
      - CELERY_BROKER_URL=amqp://admin:admin123@rabbitmq_server:5672/dhruva_host
    ports:
      - "5555:5555"
    depends_on:
      rabbitmq_server:
        condition: service_healthy
    networks:
      - dhruva-network


volumes:
  timescaledb_data:
  app_db_data:
  log_db_data:
  redis_data:

networks:
  dhruva-network:
    name: dhruva-network
