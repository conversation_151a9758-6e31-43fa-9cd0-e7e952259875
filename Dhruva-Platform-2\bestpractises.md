## Tips For Best Practices & Principles
Best practices are important for processes that you need to work correctly. They are simply the best way to do things and have been worked out through trial and error, and are found to be the most sensible way to proceed. Among other pros include: reduced costs and becoming more efficient, improving the skills of your team, using technology more effectively, reduced waste and improved quality, responding more quickly to innovations in your sector. 

Below are some of the common best practices and principles implemented by several digital public goods:

#### ICT4D
* [Principles For Digital Development](https://digitalprinciples.org/)
* [The Best Practices in the Use of ICTs in Development](https://www.ictworks.org/the-best-practices-in-the-use-of-icts-in-development-are/#.YTc0Y_wzZH5)

#### Software Development Life Cycle (SDLC)
* User stories
* Change management using version control
* Test driven development using automated tests
* Continous Integration & Continous Deployment (CI/CD)
* Code review
* Code refactoring
* Rapid application development
* [Agile development](https://www.ntaskmanager.com/blog/agile-best-practices/)
* [12 Principles Behind Agile Manifesto](https://www.agilealliance.org/agile101/12-principles-behind-the-agile-manifesto/)
* [The Twelve Factor App](https://12factor.net/)

#### Architectural Design
* [Architectural Principles](https://docs.altinn.studio/teknologi/altinnstudio/architecture/principles/#web-standards)
* Modularity and Maintainability
* Reusability and Extensibility
* Accountability & Non-repudiability
* Security & Consented Access
* Universal Access & Open APIs
* Microservices architecture
* [SOLID principles of object oriented programming](https://www.freecodecamp.org/news/solid-principles-explained-in-plain-english/)

#### Software Architectural Styles
* Multitier architecture
* Model–view–controller
* Representational state transfer (REST)
* Publish-subscribe
* Client-server (multitier architecture exhibits this style)
* Monolithic application
* Service-oriented
* Component-based
* Peer-to-peer
* Asynchronous messaging
* Event-driven
* Database-centric
* Sensor-controller-actuator
* Cloud computing patterns

#### Cloud Computing
* [AWS Best Practices For Cloud Environments](https://aws.amazon.com/blogs/publicsector/aws-well-architected-framework-best-practices-for-building-and-deploying-an-optimized-cloud-environment/)
* [Google Best Practices For Enterprise Organizations Leveraging Cloud](https://cloud.google.com/docs/enterprise/best-practices-for-enterprise-organizations)
* [Azure Best Practices in Cloud Applications](https://docs.microsoft.com/en-us/azure/architecture/best-practices/index-best-practices)
* [Design Principles For Azure Applications](https://docs.microsoft.com/en-us/azure/architecture/guide/design-principles/)

#### Artificial Intelligence/ Machine Learning (AI/ ML)
* [Google Responsible AI Practices](https://ai.google/responsibilities/responsible-ai-practices/)
* [Best Practices for ML Engineering (Google)](https://developers.google.com/machine-learning/guides/rules-of-ml)
* [Engineering best practices for Machine Learning](https://se-ml.github.io/practices/)
* [Microsoft AI guiding principles](https://query.prod.cms.rt.microsoft.com/cms/api/am/binary/RE4pKH5#:~:text=At%20Microsoft%2C%20we've%20recognized,inclusiveness%2C%20transparency%2C%20and%20accountability.)
* [The Facebook Field Guide to Machine Learning](https://research.fb.com/blog/2018/05/the-facebook-field-guide-to-machine-learning-video-series/)

#### Virtual Reality/ Augmented Reality (VR /AR)
* [Best practices & VR design principles](https://www.dummies.com/software/best-practices-and-virtual-reality-design-principles/)
* [Best practices for creating engaging VR content](https://www.thinkwithgoogle.com/marketing-strategies/video/vr-content-audience-engagement-best-practices/)

#### User Interface/ User Experience (UI /UX)
* [Human Centered Design Principles](https://jnd.org/the-four-fundamental-principles-ofhuman-centered-design/)
* [Material design](https://material.io/design/guidelines-overview)

#### Coding Styles & Standards
* [PSR-12: Extended Coding Style](https://www.php-fig.org/psr/psr-12/)
* [PEP 8](https://www.python.org/dev/peps/pep-0008/)
* [Google Style Guide](https://google.github.io/styleguide/)
* [Airbnb's Javascript Style Guide](https://github.com/airbnb/javascript)
* [Airbnb's Ruby Style Guide](https://airbnb.io/projects/ruby/)

#### Open Source 
* [Best practices For Open Source Maintainers](https://opensource.guide/best-practices/)
* [OpenSSF Best Practices Badge Program](https://bestpractices.coreinfrastructure.org/en)
* [Google Open Source](https://opensource.google/docs/)
* [Open Source Tips](https://eddiejaoude.github.io/book-open-source-tips/)
* [Standard for Public Code](https://standard.publiccode.net/) - Guidance for government open source collaboration

#### Best practices to help secure your IT resources:
* Create strong passwords for username/ password authentication
* Enable Multi-factor authentication (MFA)
* Enable resource access authorization i.e. access control rights/ permissions
* Leverage IT auditing
* Protect data at rest (data encryption, using a firewall, antivirus protection, schedule backups)
* Protect data in transit (encrypt data in transit using TLS/SSL, authenticate data integrity using TLS/SSL, use X.509 certificates to authenticate the remote end)

#### Data Principles:
* [FAIR Data Principles](https://www.go-fair.org/fair-principles/)